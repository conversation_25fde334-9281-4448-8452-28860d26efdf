import { BasePage } from './BasePage'

/**
 * Google Search Results Page Object
 */
export class GoogleSearchResultsPage extends BasePage {
  constructor() {
    super()
    
    // Selectors
    this.selectors = {
      searchBox: '[name="q"]',
      searchButton: '[name="btnK"]',
      resultsContainer: '#search',
      resultItems: '#search .g',
      firstResult: '#search .g:first-child',
      resultTitle: 'h3',
      resultLink: 'a',
      resultSnippet: '.VwiC3b',
      statsBar: '#result-stats',
      nextButton: '#pnnext',
      previousButton: '#pnprev',
      pageNumbers: '#nav a',
      searchTools: '#hdtb-tls',
      noResults: '.mnr-c'
    }
  }

  /**
   * Verify search results page is loaded
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  verifyPageLoaded() {
    this.verifyElementVisible(this.selectors.resultsContainer)
    this.verifyUrl('search')
    return this
  }

  /**
   * Verify search results are displayed
   * @param {string} searchTerm - Expected search term
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  verifySearchResults(searchTerm) {
    // Verify results container is visible
    this.verifyElementVisible(this.selectors.resultsContainer)
    
    // Verify at least one result is present
    this.getElement(this.selectors.resultItems)
      .should('have.length.greaterThan', 0)
    
    // Verify search term appears in URL
    cy.url().should('include', encodeURIComponent(searchTerm))
    
    // Verify search term appears in search box
    this.getElement(this.selectors.searchBox)
      .should('have.value', searchTerm)
    
    return this
  }

  /**
   * Get search results count
   * @returns {Cypress.Chainable} Number of search results
   */
  getResultsCount() {
    return this.getElement(this.selectors.resultItems).its('length')
  }

  /**
   * Get search result by index
   * @param {number} index - Index of result (0-based)
   * @returns {Cypress.Chainable} Search result element
   */
  getResultByIndex(index) {
    return this.getElement(this.selectors.resultItems).eq(index)
  }

  /**
   * Get first search result
   * @returns {Cypress.Chainable} First search result element
   */
  getFirstResult() {
    return this.getElement(this.selectors.firstResult)
  }

  /**
   * Click on search result by index
   * @param {number} index - Index of result to click (0-based)
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  clickResult(index = 0) {
    this.getResultByIndex(index)
      .find(this.selectors.resultTitle)
      .should('be.visible')
      .click()
    return this
  }

  /**
   * Click on first search result
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  clickFirstResult() {
    return this.clickResult(0)
  }

  /**
   * Get result title by index
   * @param {number} index - Index of result (0-based)
   * @returns {Cypress.Chainable} Result title text
   */
  getResultTitle(index) {
    return this.getResultByIndex(index)
      .find(this.selectors.resultTitle)
      .invoke('text')
  }

  /**
   * Get result URL by index
   * @param {number} index - Index of result (0-based)
   * @returns {Cypress.Chainable} Result URL
   */
  getResultUrl(index) {
    return this.getResultByIndex(index)
      .find(this.selectors.resultLink)
      .invoke('attr', 'href')
  }

  /**
   * Verify result contains text
   * @param {number} index - Index of result (0-based)
   * @param {string} expectedText - Expected text in result
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  verifyResultContainsText(index, expectedText) {
    this.getResultByIndex(index)
      .should('contain.text', expectedText)
    return this
  }

  /**
   * Perform new search from results page
   * @param {string} searchTerm - New search term
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  searchAgain(searchTerm) {
    this.getElement(this.selectors.searchBox)
      .clear()
      .type(searchTerm)
      .type('{enter}')
    
    this.verifyPageLoaded()
    return this
  }

  /**
   * Go to next page of results
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  goToNextPage() {
    this.getElement(this.selectors.nextButton)
      .should('be.visible')
      .click()
    
    this.verifyPageLoaded()
    return this
  }

  /**
   * Go to previous page of results
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  goToPreviousPage() {
    this.getElement(this.selectors.previousButton)
      .should('be.visible')
      .click()
    
    this.verifyPageLoaded()
    return this
  }

  /**
   * Verify search statistics are displayed
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  verifySearchStats() {
    this.verifyElementVisible(this.selectors.statsBar)
    this.getElement(this.selectors.statsBar)
      .should('contain.text', 'results')
    return this
  }

  /**
   * Verify no results message
   * @returns {GoogleSearchResultsPage} Page object instance
   */
  verifyNoResults() {
    this.getElement(this.selectors.noResults)
      .should('be.visible')
      .should('contain.text', 'did not match any documents')
    return this
  }
}
