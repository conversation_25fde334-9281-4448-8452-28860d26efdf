const { defineConfig } = require('cypress')

module.exports = defineConfig({
  e2e: {
    // Base URL configuration
    baseUrl: 'https://www.google.com',
    
    // Viewport configuration
    viewportWidth: 1920,
    viewportHeight: 1080,
    
    // Test configuration
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,
    
    // Retry configuration
    retries: {
      runMode: 2,
      openMode: 0
    },
    
    // Video and screenshot configuration
    video: true,
    videoCompression: 32,
    videosFolder: 'cypress/videos',
    screenshotOnRunFailure: true,
    screenshotsFolder: 'cypress/screenshots',
    
    // Test files configuration
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    fixturesFolder: 'cypress/fixtures',
    
    // Browser configuration
    chromeWebSecurity: false,
    modifyObstructiveCode: false,
    
    // Experimental features
    experimentalStudio: true,
    experimentalWebKitSupport: false,
    
    // Environment variables
    env: {
      // Test environment settings
      testEnvironment: 'development',
      apiTimeout: 10000,
      
      // Feature flags
      enableRetries: true,
      enableScreenshots: true,
      enableVideos: true,
      
      // Test data
      defaultSearchTerm: 'Cypress testing',
      maxRetries: 3,
      
      // Timeouts
      shortTimeout: 5000,
      mediumTimeout: 10000,
      longTimeout: 30000
    },
    
    setupNodeEvents(on, config) {
      // Task registration
      on('task', {
        // Log messages to console
        log(message) {
          console.log(message)
          return null
        },
        
        // Generate timestamp
        generateTimestamp() {
          return new Date().toISOString()
        },
        
        // File system operations
        readFile(filename) {
          const fs = require('fs')
          const path = require('path')
          
          try {
            return fs.readFileSync(path.resolve(filename), 'utf8')
          } catch (error) {
            return null
          }
        },
        
        // Database operations (if needed)
        queryDatabase(query) {
          // Implement database queries if needed
          return null
        }
      })
      
      // Plugin registration
      
      // Mochawesome reporter configuration
      on('before:browser:launch', (browser = {}, launchOptions) => {
        // Chrome configuration
        if (browser.name === 'chrome') {
          launchOptions.args.push('--disable-dev-shm-usage')
          launchOptions.args.push('--no-sandbox')
          launchOptions.args.push('--disable-gpu')
          launchOptions.args.push('--disable-web-security')
          launchOptions.args.push('--allow-running-insecure-content')
          launchOptions.args.push('--disable-features=VizDisplayCompositor')
        }
        
        // Firefox configuration
        if (browser.name === 'firefox') {
          launchOptions.preferences['dom.webnotifications.enabled'] = false
          launchOptions.preferences['dom.push.enabled'] = false
        }
        
        return launchOptions
      })
      
      // After screenshot event
      on('after:screenshot', (details) => {
        console.log('Screenshot taken:', details.path)
      })
      
      // Before spec event
      on('before:spec', (spec) => {
        console.log('Running spec:', spec.name)
      })
      
      // After spec event
      on('after:spec', (spec, results) => {
        console.log('Spec completed:', spec.name)
        console.log('Results:', {
          tests: results.stats.tests,
          passes: results.stats.passes,
          failures: results.stats.failures,
          duration: results.stats.duration
        })
      })
      
      // Configuration based on environment
      if (config.env.testEnvironment === 'ci') {
        config.video = true
        config.screenshotOnRunFailure = true
        config.retries.runMode = 2
      }
      
      if (config.env.testEnvironment === 'development') {
        config.video = false
        config.screenshotOnRunFailure = true
        config.retries.runMode = 0
      }
      
      return config
    }
  },
  
  // Component testing configuration (if needed)
  component: {
    devServer: {
      framework: 'react',
      bundler: 'webpack'
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.js'
  }
})
