// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

/**
 * Custom command to wait for page to load completely
 */
Cypress.Commands.add('waitForPageLoad', () => {
  cy.window().should('have.property', 'document')
  cy.document().should('have.property', 'readyState', 'complete')
})

/**
 * Custom command to handle cookie consent banners
 */
Cypress.Commands.add('handleCookieConsent', () => {
  // Handle common cookie consent patterns
  cy.get('body').then(($body) => {
    if ($body.find('[id*="cookie"], [class*="cookie"], [id*="consent"], [class*="consent"]').length > 0) {
      cy.get('[id*="cookie"], [class*="cookie"], [id*="consent"], [class*="consent"]')
        .find('button, a')
        .contains(/accept|agree|ok|got it/i)
        .first()
        .click({ force: true })
    }
  })
})

/**
 * Custom command to take a screenshot with timestamp
 */
Cypress.Commands.add('takeScreenshotWithTimestamp', (name) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  cy.screenshot(`${name}-${timestamp}`)
})

/**
 * Custom command to retry an action
 */
Cypress.Commands.add('retryAction', (action, maxRetries = 3) => {
  let attempts = 0
  
  const performAction = () => {
    attempts++
    try {
      action()
    } catch (error) {
      if (attempts < maxRetries) {
        cy.wait(1000)
        performAction()
      } else {
        throw error
      }
    }
  }
  
  performAction()
})

/**
 * Custom command to wait for element to be stable (not moving)
 */
Cypress.Commands.add('waitForStableElement', (selector, timeout = 5000) => {
  let previousPosition = null
  
  cy.get(selector, { timeout }).then(($el) => {
    const checkStability = () => {
      const currentPosition = $el.offset()
      
      if (previousPosition && 
          currentPosition.top === previousPosition.top && 
          currentPosition.left === previousPosition.left) {
        return true
      }
      
      previousPosition = currentPosition
      return false
    }
    
    cy.waitUntil(() => checkStability(), {
      timeout,
      interval: 100,
      errorMsg: `Element ${selector} did not stabilize within ${timeout}ms`
    })
  })
})
