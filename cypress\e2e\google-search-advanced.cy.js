import { GoogleHomePage, GoogleSearchResultsPage } from '../support/pages'
import { getRandomSearchTerm, formatSearchTermForUrl } from '../support/utils/dataHelpers'
import { logTestStep, retryOperation } from '../support/utils/testHelpers'

describe('Google Search Advanced Tests', () => {
  let googleHomePage
  let testData

  before(() => {
    cy.fixture('testData').then((data) => {
      testData = data
    })
  })

  beforeEach(() => {
    googleHomePage = new GoogleHomePage()
    googleHomePage.visit()
  })

  describe('Advanced Search Scenarios', () => {
    it('should perform search with quotes for exact match', () => {
      const exactSearchTerm = '"Cypress end-to-end testing"'
      logTestStep(`Performing exact match search: ${exactSearchTerm}`)
      
      const searchResultsPage = googleHomePage.search(exactSearchTerm)
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(exactSearchTerm)
      
      // Verify results contain the exact phrase
      searchResultsPage.getResultsCount().should('be.greaterThan', 0)
      
      logTestStep('Exact match search completed')
    })

    it('should perform search with boolean operators', () => {
      const booleanSearchTerm = 'Cypress AND testing AND automation'
      logTestStep(`Performing boolean search: ${booleanSearchTerm}`)
      
      const searchResultsPage = googleHomePage.search(booleanSearchTerm)
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(booleanSearchTerm)
      
      logTestStep('Boolean search completed')
    })

    it('should perform search with exclusion operator', () => {
      const exclusionSearchTerm = 'testing framework -selenium'
      logTestStep(`Performing exclusion search: ${exclusionSearchTerm}`)
      
      const searchResultsPage = googleHomePage.search(exclusionSearchTerm)
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(exclusionSearchTerm)
      
      logTestStep('Exclusion search completed')
    })

    it('should handle long search queries', () => {
      const longSearchTerm = 'How to implement end-to-end testing automation framework using Cypress with best practices for continuous integration and deployment in Azure DevOps'
      logTestStep(`Performing long query search: ${longSearchTerm.substring(0, 50)}...`)
      
      const searchResultsPage = googleHomePage.search(longSearchTerm)
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(longSearchTerm)
      
      logTestStep('Long query search completed')
    })
  })

  describe('Search Performance and Reliability', () => {
    it('should handle multiple rapid searches', () => {
      logTestStep('Testing rapid search performance')
      
      const searchTerms = [
        'Cypress testing',
        'JavaScript automation',
        'E2E testing tools'
      ]
      
      searchTerms.forEach((term, index) => {
        logTestStep(`Rapid search ${index + 1}: ${term}`)
        
        if (index === 0) {
          const searchResultsPage = googleHomePage.search(term)
          searchResultsPage.verifySearchResults(term)
        } else {
          const searchResultsPage = new GoogleSearchResultsPage()
          searchResultsPage.searchAgain(term)
          searchResultsPage.verifySearchResults(term)
        }
        
        // Minimal wait between searches
        cy.wait(200)
      })
      
      logTestStep('Rapid search test completed')
    })

    it('should retry failed searches', () => {
      logTestStep('Testing search retry mechanism')
      
      const searchTerm = 'Cypress automation framework'
      
      retryOperation(() => {
        const searchResultsPage = googleHomePage.search(searchTerm)
        searchResultsPage.verifySearchResults(searchTerm)
        return searchResultsPage
      }, 3, 1000)
      
      logTestStep('Search retry test completed')
    })

    it('should handle network delays gracefully', () => {
      logTestStep('Testing network delay handling')
      
      // Simulate slow network by adding intercept
      cy.intercept('GET', '**/search**', (req) => {
        req.reply((res) => {
          // Add delay to simulate slow network
          return new Promise((resolve) => {
            setTimeout(() => resolve(res), 2000)
          })
        })
      }).as('slowSearch')
      
      const searchTerm = 'Network delay test'
      const searchResultsPage = googleHomePage.search(searchTerm)
      
      // Wait for the slow request
      cy.wait('@slowSearch', { timeout: 10000 })
      
      searchResultsPage.verifySearchResults(searchTerm)
      
      logTestStep('Network delay test completed')
    })
  })

  describe('Cross-browser Compatibility', () => {
    it('should work consistently across different viewport sizes', () => {
      const viewports = [
        { width: 1920, height: 1080, name: 'Desktop' },
        { width: 1366, height: 768, name: 'Laptop' },
        { width: 768, height: 1024, name: 'Tablet' },
        { width: 375, height: 667, name: 'Mobile' }
      ]
      
      viewports.forEach((viewport) => {
        logTestStep(`Testing on ${viewport.name} viewport: ${viewport.width}x${viewport.height}`)
        
        cy.viewport(viewport.width, viewport.height)
        
        googleHomePage.visit()
        const searchResultsPage = googleHomePage.search('Responsive testing')
        
        searchResultsPage
          .verifyPageLoaded()
          .verifySearchResults('Responsive testing')
        
        logTestStep(`${viewport.name} viewport test completed`)
      })
    })
  })

  describe('Accessibility and Usability', () => {
    it('should be keyboard navigable', () => {
      logTestStep('Testing keyboard navigation')
      
      // Focus on search box using Tab
      cy.get('body').tab()
      cy.focused().should('have.attr', 'name', 'q')
      
      // Type search term
      cy.focused().type('Keyboard navigation test')
      
      // Press Enter to search
      cy.focused().type('{enter}')
      
      const searchResultsPage = new GoogleSearchResultsPage()
      searchResultsPage.verifySearchResults('Keyboard navigation test')
      
      logTestStep('Keyboard navigation test completed')
    })

    it('should have proper ARIA labels and roles', () => {
      logTestStep('Testing accessibility attributes')
      
      googleHomePage.getSearchBox()
        .should('have.attr', 'role')
        .or('have.attr', 'aria-label')
        .or('have.attr', 'title')
      
      logTestStep('Accessibility test completed')
    })
  })

  describe('Data-driven Testing', () => {
    it('should test all predefined search terms', () => {
      logTestStep('Running data-driven search tests')
      
      testData.searchTerms.forEach((searchTerm, index) => {
        logTestStep(`Data-driven test ${index + 1}/${testData.searchTerms.length}: ${searchTerm}`)
        
        if (index === 0) {
          const searchResultsPage = googleHomePage.search(searchTerm)
          searchResultsPage.verifySearchResults(searchTerm)
        } else {
          googleHomePage.visit()
          const searchResultsPage = googleHomePage.search(searchTerm)
          searchResultsPage.verifySearchResults(searchTerm)
        }
        
        // Verify URL formatting
        cy.url().should('include', formatSearchTermForUrl(searchTerm))
        
        cy.wait(500) // Brief pause between tests
      })
      
      logTestStep('Data-driven testing completed')
    })

    it('should generate and test random search combinations', () => {
      logTestStep('Testing random search combinations')
      
      const numberOfTests = 3
      
      for (let i = 0; i < numberOfTests; i++) {
        const randomTerm = getRandomSearchTerm()
        logTestStep(`Random test ${i + 1}/${numberOfTests}: ${randomTerm}`)
        
        googleHomePage.visit()
        const searchResultsPage = googleHomePage.search(randomTerm)
        
        searchResultsPage
          .verifyPageLoaded()
          .verifySearchResults(randomTerm)
        
        // Verify we get some results
        searchResultsPage.getResultsCount().should('be.greaterThan', 0)
      }
      
      logTestStep('Random search testing completed')
    })
  })

  afterEach(() => {
    // Clean up and take screenshot on failure
    if (Cypress.currentTest.state === 'failed') {
      cy.takeScreenshotWithTimestamp(`advanced-failed-${Cypress.currentTest.title}`)
    }
  })
})
