/**
 * Report helper utilities for test reporting and analytics
 */

/**
 * Generate test report data
 * @param {Object} testResult - Test result object
 * @returns {Object} Formatted report data
 */
export const generateTestReportData = (testResult) => {
  return {
    testName: testResult.title,
    status: testResult.state,
    duration: testResult.duration,
    timestamp: new Date().toISOString(),
    browser: Cypress.browser.name,
    viewport: {
      width: Cypress.config('viewportWidth'),
      height: Cypress.config('viewportHeight')
    },
    environment: Cypress.env('testEnvironment'),
    retries: testResult.currentRetry || 0
  }
}

/**
 * Log test metrics
 * @param {string} metricName - Name of the metric
 * @param {*} value - Metric value
 * @param {Object} metadata - Additional metadata
 */
export const logTestMetric = (metricName, value, metadata = {}) => {
  const metric = {
    name: metricName,
    value: value,
    timestamp: new Date().toISOString(),
    testTitle: Cypress.currentTest?.title || 'Unknown',
    ...metadata
  }
  
  cy.task('log', `METRIC: ${JSON.stringify(metric)}`)
}

/**
 * Track test performance
 * @param {string} operationName - Name of the operation
 * @param {Function} operation - Operation to track
 * @returns {*} Operation result
 */
export const trackPerformance = (operationName, operation) => {
  const startTime = performance.now()
  
  const result = operation()
  
  const endTime = performance.now()
  const duration = endTime - startTime
  
  logTestMetric('performance', duration, {
    operation: operationName,
    unit: 'milliseconds'
  })
  
  return result
}

/**
 * Generate test summary
 * @param {Array} testResults - Array of test results
 * @returns {Object} Test summary
 */
export const generateTestSummary = (testResults) => {
  const summary = {
    total: testResults.length,
    passed: testResults.filter(t => t.state === 'passed').length,
    failed: testResults.filter(t => t.state === 'failed').length,
    skipped: testResults.filter(t => t.state === 'pending').length,
    totalDuration: testResults.reduce((sum, t) => sum + (t.duration || 0), 0),
    timestamp: new Date().toISOString()
  }
  
  summary.passRate = summary.total > 0 ? (summary.passed / summary.total * 100).toFixed(2) : 0
  
  return summary
}

/**
 * Export test results to JSON
 * @param {Array} testResults - Array of test results
 * @param {string} filename - Output filename
 */
export const exportTestResults = (testResults, filename = 'test-results.json') => {
  const results = {
    summary: generateTestSummary(testResults),
    tests: testResults.map(generateTestReportData),
    environment: {
      browser: Cypress.browser,
      viewport: {
        width: Cypress.config('viewportWidth'),
        height: Cypress.config('viewportHeight')
      },
      baseUrl: Cypress.config('baseUrl'),
      testEnvironment: Cypress.env('testEnvironment')
    },
    timestamp: new Date().toISOString()
  }
  
  cy.writeFile(`cypress/results/${filename}`, results)
}

/**
 * Create HTML report
 * @param {Object} testData - Test data for report
 * @returns {string} HTML report content
 */
export const createHtmlReport = (testData) => {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Cypress Test Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
            .summary { display: flex; gap: 20px; margin: 20px 0; }
            .metric { background: #e8f4fd; padding: 15px; border-radius: 5px; text-align: center; }
            .passed { background: #d4edda; }
            .failed { background: #f8d7da; }
            .test-list { margin-top: 20px; }
            .test-item { padding: 10px; border-bottom: 1px solid #eee; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Cypress Test Report</h1>
            <p>Generated: ${testData.timestamp}</p>
            <p>Environment: ${testData.environment.testEnvironment}</p>
            <p>Browser: ${testData.environment.browser.name} ${testData.environment.browser.version}</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>${testData.summary.total}</h3>
                <p>Total Tests</p>
            </div>
            <div class="metric passed">
                <h3>${testData.summary.passed}</h3>
                <p>Passed</p>
            </div>
            <div class="metric failed">
                <h3>${testData.summary.failed}</h3>
                <p>Failed</p>
            </div>
            <div class="metric">
                <h3>${testData.summary.passRate}%</h3>
                <p>Pass Rate</p>
            </div>
        </div>
        
        <div class="test-list">
            <h2>Test Results</h2>
            ${testData.tests.map(test => `
                <div class="test-item ${test.status}">
                    <h4>${test.testName}</h4>
                    <p>Status: ${test.status} | Duration: ${test.duration}ms | Retries: ${test.retries}</p>
                </div>
            `).join('')}
        </div>
    </body>
    </html>
  `
  
  return html
}
