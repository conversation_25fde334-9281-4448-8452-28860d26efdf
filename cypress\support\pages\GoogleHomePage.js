import { BasePage } from './BasePage'

/**
 * Google Home Page Object
 */
export class GoogleHomePage extends BasePage {
  constructor() {
    super()
    this.url = 'https://www.google.com'
    
    // Selectors
    this.selectors = {
      logo: 'img[alt*="Google"]',
      searchBox: '[name="q"]',
      searchButton: '[name="btnK"]',
      luckyButton: '[name="btnI"]',
      languageLinks: '#SIvCob a',
      footer: '#fbar',
      cookieConsent: '[id*="cookie"], [class*="cookie"], [id*="consent"], [class*="consent"]'
    }
  }

  /**
   * Navigate to Google homepage
   * @returns {GoogleHomePage} Page object instance
   */
  visit() {
    super.visit(this.url)
    this.handleCookieConsent()
    return this
  }

  /**
   * Verify Google homepage is loaded
   * @returns {GoogleHomePage} Page object instance
   */
  verifyPageLoaded() {
    this.verifyElementVisible(this.selectors.logo)
    this.verifyElementVisible(this.selectors.searchBox)
    this.verifyElementVisible(this.selectors.searchButton)
    this.verifyTitle('Google')
    return this
  }

  /**
   * Get search box element
   * @returns {Cypress.Chainable} Search box element
   */
  getSearchBox() {
    return this.getElement(this.selectors.searchBox)
  }

  /**
   * Get search button element
   * @returns {Cypress.Chainable} Search button element
   */
  getSearchButton() {
    return this.getElement(this.selectors.searchButton)
  }

  /**
   * Get "I'm Feeling Lucky" button element
   * @returns {Cypress.Chainable} Lucky button element
   */
  getLuckyButton() {
    return this.getElement(this.selectors.luckyButton)
  }

  /**
   * Type search term in search box
   * @param {string} searchTerm - Term to search for
   * @returns {GoogleHomePage} Page object instance
   */
  typeSearchTerm(searchTerm) {
    this.getSearchBox()
      .should('be.visible')
      .clear()
      .type(searchTerm)
    return this
  }

  /**
   * Click search button
   * @returns {GoogleSearchResultsPage} Search results page object
   */
  clickSearchButton() {
    this.getSearchButton().click()
    // Return search results page object
    const { GoogleSearchResultsPage } = require('./GoogleSearchResultsPage')
    return new GoogleSearchResultsPage()
  }

  /**
   * Press Enter to search
   * @returns {GoogleSearchResultsPage} Search results page object
   */
  pressEnterToSearch() {
    this.getSearchBox().type('{enter}')
    // Return search results page object
    const { GoogleSearchResultsPage } = require('./GoogleSearchResultsPage')
    return new GoogleSearchResultsPage()
  }

  /**
   * Perform search by typing term and pressing Enter
   * @param {string} searchTerm - Term to search for
   * @returns {GoogleSearchResultsPage} Search results page object
   */
  search(searchTerm) {
    this.typeSearchTerm(searchTerm)
    cy.wait(500) // Wait for suggestions to appear
    return this.pressEnterToSearch()
  }

  /**
   * Click "I'm Feeling Lucky" button
   * @returns {GoogleHomePage} Page object instance
   */
  clickLuckyButton() {
    this.getLuckyButton().click()
    return this
  }

  /**
   * Verify search suggestions appear
   * @returns {GoogleHomePage} Page object instance
   */
  verifySearchSuggestions() {
    // Google search suggestions appear in a dropdown
    cy.get('[role="listbox"]', { timeout: 5000 }).should('be.visible')
    return this
  }

  /**
   * Select search suggestion by index
   * @param {number} index - Index of suggestion to select (0-based)
   * @returns {GoogleSearchResultsPage} Search results page object
   */
  selectSearchSuggestion(index = 0) {
    cy.get('[role="listbox"] [role="option"]')
      .eq(index)
      .click()
    
    const { GoogleSearchResultsPage } = require('./GoogleSearchResultsPage')
    return new GoogleSearchResultsPage()
  }

  /**
   * Verify language links are present
   * @returns {GoogleHomePage} Page object instance
   */
  verifyLanguageLinks() {
    this.getElement(this.selectors.languageLinks)
      .should('have.length.greaterThan', 0)
    return this
  }

  /**
   * Verify footer is present
   * @returns {GoogleHomePage} Page object instance
   */
  verifyFooter() {
    this.verifyElementVisible(this.selectors.footer)
    return this
  }
}
