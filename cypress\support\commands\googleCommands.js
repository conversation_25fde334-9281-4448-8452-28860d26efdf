/**
 * Custom commands specific to Google Search functionality
 */

/**
 * Navigate to Google homepage
 */
Cypress.Commands.add('visitGoogle', () => {
  cy.visit('https://www.google.com')
  cy.waitForPageLoad()
  cy.handleCookieConsent()
})

/**
 * Perform a search on Google
 * @param {string} searchTerm - The term to search for
 */
Cypress.Commands.add('searchGoogle', (searchTerm) => {
  cy.get('[name="q"]', { timeout: 10000 })
    .should('be.visible')
    .clear()
    .type(searchTerm)
  
  // Wait for suggestions to appear and then press Enter
  cy.wait(500)
  cy.get('[name="q"]').type('{enter}')
  
  // Wait for search results to load
  cy.get('#search', { timeout: 15000 }).should('be.visible')
})

/**
 * Verify search results are displayed
 * @param {string} expectedTerm - The term that should appear in results
 */
Cypress.Commands.add('verifySearchResults', (expectedTerm) => {
  // Verify search results container is visible
  cy.get('#search').should('be.visible')
  
  // Verify at least one search result is present
  cy.get('#search .g').should('have.length.greaterThan', 0)
  
  // Verify the search term appears in the page
  cy.get('body').should('contain.text', expectedTerm)
  
  // Verify URL contains search parameter
  cy.url().should('include', 'search')
  cy.url().should('include', encodeURIComponent(expectedTerm))
})

/**
 * Click on the first search result
 */
Cypress.Commands.add('clickFirstSearchResult', () => {
  cy.get('#search .g:first-child h3')
    .should('be.visible')
    .click()
})

/**
 * Get search result count
 */
Cypress.Commands.add('getSearchResultCount', () => {
  return cy.get('#search .g').its('length')
})

/**
 * Verify Google homepage elements
 */
Cypress.Commands.add('verifyGoogleHomepage', () => {
  // Verify Google logo
  cy.get('img[alt*="Google"]').should('be.visible')
  
  // Verify search box
  cy.get('[name="q"]').should('be.visible')
  
  // Verify search buttons
  cy.get('[name="btnK"]').should('exist')
})
