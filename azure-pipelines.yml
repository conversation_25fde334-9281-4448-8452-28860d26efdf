# Azure DevOps Pipeline for Cypress Automation Tests
# This pipeline runs Cypress tests in multiple browsers and environments

trigger:
  branches:
    include:
      - main
      - develop
      - feature/*
  paths:
    include:
      - cypress/**
      - package.json
      - cypress.config.js
      - azure-pipelines.yml

pr:
  branches:
    include:
      - main
      - develop
  paths:
    include:
      - cypress/**
      - package.json
      - cypress.config.js

variables:
  # Node.js version
  nodeVersion: '18.x'
  
  # Cypress environment
  cypressEnvironment: 'ci'
  
  # Test results directory
  testResultsDirectory: '$(System.DefaultWorkingDirectory)/cypress/results'
  
  # Artifacts directory
  artifactsDirectory: '$(System.DefaultWorkingDirectory)/cypress/artifacts'

stages:
  - stage: Test
    displayName: 'Run Cypress Tests'
    jobs:
      - job: ChromeTests
        displayName: 'Chrome Browser Tests'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '$(nodeVersion)'
          
          - task: Cache@2
            displayName: 'Cache node_modules'
            inputs:
              key: 'npm | "$(Agent.OS)" | package-lock.json'
              restoreKeys: |
                npm | "$(Agent.OS)"
                npm
              path: '$(System.DefaultWorkingDirectory)/node_modules'
          
          - script: |
              npm ci
            displayName: 'Install dependencies'
          
          - script: |
              npx cypress install
            displayName: 'Install Cypress binary'
          
          - script: |
              mkdir -p $(testResultsDirectory)
              mkdir -p $(artifactsDirectory)
            displayName: 'Create directories'
          
          - script: |
              npm run test:ci
            displayName: 'Run Cypress tests in Chrome'
            env:
              CYPRESS_testEnvironment: $(cypressEnvironment)
              CYPRESS_video: true
              CYPRESS_screenshotOnRunFailure: true
          
          - task: PublishTestResults@2
            displayName: 'Publish test results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '$(testResultsDirectory)/*.xml'
              testRunTitle: 'Cypress Tests - Chrome'
              mergeTestResults: true
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish screenshots'
            condition: always()
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/screenshots'
              artifactName: 'cypress-screenshots-chrome'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish videos'
            condition: always()
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/videos'
              artifactName: 'cypress-videos-chrome'

      - job: FirefoxTests
        displayName: 'Firefox Browser Tests'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '$(nodeVersion)'
          
          - task: Cache@2
            displayName: 'Cache node_modules'
            inputs:
              key: 'npm | "$(Agent.OS)" | package-lock.json'
              restoreKeys: |
                npm | "$(Agent.OS)"
                npm
              path: '$(System.DefaultWorkingDirectory)/node_modules'
          
          - script: |
              npm ci
            displayName: 'Install dependencies'
          
          - script: |
              npx cypress install
            displayName: 'Install Cypress binary'
          
          - script: |
              mkdir -p $(testResultsDirectory)
              mkdir -p $(artifactsDirectory)
            displayName: 'Create directories'
          
          - script: |
              npm run test:firefox
            displayName: 'Run Cypress tests in Firefox'
            env:
              CYPRESS_testEnvironment: $(cypressEnvironment)
              CYPRESS_video: true
              CYPRESS_screenshotOnRunFailure: true
          
          - task: PublishTestResults@2
            displayName: 'Publish test results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '$(testResultsDirectory)/*.xml'
              testRunTitle: 'Cypress Tests - Firefox'
              mergeTestResults: true
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish screenshots'
            condition: always()
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/screenshots'
              artifactName: 'cypress-screenshots-firefox'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish videos'
            condition: always()
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/videos'
              artifactName: 'cypress-videos-firefox'

      - job: EdgeTests
        displayName: 'Edge Browser Tests'
        pool:
          vmImage: 'windows-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '$(nodeVersion)'
          
          - task: Cache@2
            displayName: 'Cache node_modules'
            inputs:
              key: 'npm | "$(Agent.OS)" | package-lock.json'
              restoreKeys: |
                npm | "$(Agent.OS)"
                npm
              path: '$(System.DefaultWorkingDirectory)/node_modules'
          
          - script: |
              npm ci
            displayName: 'Install dependencies'
          
          - script: |
              npx cypress install
            displayName: 'Install Cypress binary'
          
          - script: |
              mkdir $(testResultsDirectory)
              mkdir $(artifactsDirectory)
            displayName: 'Create directories'
          
          - script: |
              npm run test:edge
            displayName: 'Run Cypress tests in Edge'
            env:
              CYPRESS_testEnvironment: $(cypressEnvironment)
              CYPRESS_video: true
              CYPRESS_screenshotOnRunFailure: true
          
          - task: PublishTestResults@2
            displayName: 'Publish test results'
            condition: always()
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '$(testResultsDirectory)/*.xml'
              testRunTitle: 'Cypress Tests - Edge'
              mergeTestResults: true
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish screenshots'
            condition: always()
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/screenshots'
              artifactName: 'cypress-screenshots-edge'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish videos'
            condition: always()
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/videos'
              artifactName: 'cypress-videos-edge'

  - stage: Report
    displayName: 'Generate Reports'
    dependsOn: Test
    condition: always()
    jobs:
      - job: GenerateReports
        displayName: 'Generate Test Reports'
        pool:
          vmImage: 'ubuntu-latest'
        
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '$(nodeVersion)'
          
          - script: |
              npm ci
            displayName: 'Install dependencies'
          
          - task: DownloadBuildArtifacts@0
            displayName: 'Download all artifacts'
            inputs:
              buildType: 'current'
              downloadType: 'all'
              downloadPath: '$(System.ArtifactsDirectory)'
          
          - script: |
              # Generate consolidated report
              echo "Generating consolidated test report..."
              # Add custom report generation logic here
            displayName: 'Generate consolidated report'
          
          - task: PublishBuildArtifacts@1
            displayName: 'Publish consolidated report'
            inputs:
              pathToPublish: '$(System.DefaultWorkingDirectory)/cypress/reports'
              artifactName: 'cypress-reports'
