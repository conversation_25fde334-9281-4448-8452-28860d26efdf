/**
 * Data helper utilities for test data management
 */

/**
 * Generate random search terms
 * @returns {string} Random search term
 */
export const getRandomSearchTerm = () => {
  const searchTerms = [
    'Cypress automation testing',
    'JavaScript testing framework',
    'End-to-end testing best practices',
    'Azure DevOps CI/CD',
    'Test automation tools',
    'Quality assurance automation',
    'Web application testing',
    'Selenium vs Cypress',
    'Modern testing frameworks',
    'Continuous integration testing'
  ]
  
  return searchTerms[Math.floor(Math.random() * searchTerms.length)]
}

/**
 * Generate test data with timestamp
 * @param {string} prefix - Prefix for the data
 * @returns {string} Data with timestamp
 */
export const generateTestDataWithTimestamp = (prefix = 'test') => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  return `${prefix}-${timestamp}`
}

/**
 * Load test data from fixtures
 * @param {string} fixtureName - Name of the fixture file
 * @returns {Promise} Promise that resolves to fixture data
 */
export const loadTestData = (fixtureName) => {
  return cy.fixture(fixtureName)
}

/**
 * Get search terms from test data
 * @returns {Promise} Promise that resolves to search terms array
 */
export const getSearchTerms = () => {
  return cy.fixture('testData').then((data) => data.searchTerms)
}

/**
 * Get random item from array
 * @param {Array} array - Array to select from
 * @returns {*} Random item from array
 */
export const getRandomItem = (array) => {
  return array[Math.floor(Math.random() * array.length)]
}

/**
 * Format search term for URL
 * @param {string} searchTerm - Search term to format
 * @returns {string} URL encoded search term
 */
export const formatSearchTermForUrl = (searchTerm) => {
  return encodeURIComponent(searchTerm.toLowerCase().replace(/\s+/g, '+'))
}

/**
 * Validate search term
 * @param {string} searchTerm - Search term to validate
 * @returns {boolean} True if valid, false otherwise
 */
export const isValidSearchTerm = (searchTerm) => {
  return typeof searchTerm === 'string' && 
         searchTerm.trim().length > 0 && 
         searchTerm.length <= 100
}
