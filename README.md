# Cypress Automation Project

Un proyecto de automatización de pruebas end-to-end usando Cypress con las mejores prácticas, patrones de diseño y configuración para CI/CD con Azure DevOps.

## 🚀 Características

- ✅ **Page Object Model (POM)** - Patrón de diseño para mantenibilidad
- ✅ **Comandos personalizados** - Funciones reutilizables específicas del dominio
- ✅ **Utilidades y helpers** - Funciones de apoyo para testing
- ✅ **Configuración multi-ambiente** - Desarrollo, staging, producción, CI
- ✅ **CI/CD con Azure DevOps** - Pipeline automatizado
- ✅ **Soporte multi-browser** - Chrome, Firefox, Edge
- ✅ **Reportes detallados** - Screenshots, videos, reportes HTML
- ✅ **Data-driven testing** - Tests basados en datos
- ✅ **Manejo de errores** - Reintentos y recuperación

## 📁 Estructura del Proyecto

```
CypressAutomation/
├── cypress/
│   ├── e2e/                          # Tests end-to-end
│   │   ├── google-search.cy.js       # Tests básicos de búsqueda
│   │   └── google-search-advanced.cy.js # Tests avanzados
│   ├── fixtures/                     # Datos de prueba
│   │   ├── testData.json             # Datos principales
│   │   └── ciTestData.json           # Datos para CI
│   ├── support/                      # Archivos de soporte
│   │   ├── commands/                 # Comandos personalizados
│   │   │   └── googleCommands.js     # Comandos específicos de Google
│   │   ├── pages/                    # Page Object Model
│   │   │   ├── BasePage.js           # Clase base
│   │   │   ├── GoogleHomePage.js     # Página de inicio de Google
│   │   │   ├── GoogleSearchResultsPage.js # Página de resultados
│   │   │   └── index.js              # Exportaciones
│   │   ├── utils/                    # Utilidades
│   │   │   ├── dataHelpers.js        # Helpers de datos
│   │   │   ├── testHelpers.js        # Helpers de testing
│   │   │   ├── reportHelpers.js      # Helpers de reportes
│   │   │   └── environmentHelpers.js # Helpers de ambiente
│   │   ├── commands.js               # Comandos globales
│   │   └── e2e.js                    # Configuración global
│   ├── screenshots/                  # Screenshots automáticos
│   ├── videos/                       # Videos de ejecución
│   ├── results/                      # Resultados de tests
│   └── reports/                      # Reportes generados
├── scripts/
│   └── ci-setup.js                   # Script de configuración CI
├── .github/workflows/
│   └── cypress.yml                   # GitHub Actions (alternativo)
├── azure-pipelines.yml               # Pipeline de Azure DevOps
├── cypress.config.js                 # Configuración principal
├── package.json                      # Dependencias y scripts
└── README.md                         # Este archivo
```

## 🛠️ Instalación

### Prerrequisitos

- Node.js 16+ 
- npm 8+
- Git

### Pasos de instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd CypressAutomation
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Verificar instalación de Cypress**
   ```bash
   npx cypress verify
   ```

## 🎯 Uso

### Ejecutar tests localmente

#### Modo interactivo (recomendado para desarrollo)
```bash
npm run cy:open
```

#### Modo headless
```bash
npm run cy:run
```

#### Tests por browser específico
```bash
npm run cy:run:chrome    # Chrome
npm run cy:run:firefox   # Firefox
npm run cy:run:edge      # Edge
```

#### Tests con cabeza visible
```bash
npm run cy:run:headed
```

### Scripts disponibles

| Script | Descripción |
|--------|-------------|
| `npm run cy:open` | Abre Cypress en modo interactivo |
| `npm run cy:run` | Ejecuta todos los tests en modo headless |
| `npm run test` | Alias para cy:run |
| `npm run test:chrome` | Ejecuta tests en Chrome |
| `npm run test:firefox` | Ejecuta tests en Firefox |
| `npm run test:edge` | Ejecuta tests en Edge |
| `npm run test:ci` | Ejecuta tests en modo CI |
| `npm run clean:reports` | Limpia reportes anteriores |

## 🧪 Tests Incluidos

### Tests Básicos (`google-search.cy.js`)
- ✅ Carga de página de Google
- ✅ Verificación de elementos de búsqueda
- ✅ Búsqueda básica con Enter
- ✅ Búsqueda con botón de búsqueda
- ✅ Múltiples términos de búsqueda
- ✅ Interacción con resultados
- ✅ Manejo de errores

### Tests Avanzados (`google-search-advanced.cy.js`)
- ✅ Búsquedas con comillas (coincidencia exacta)
- ✅ Operadores booleanos (AND, OR)
- ✅ Operadores de exclusión (-)
- ✅ Consultas largas
- ✅ Búsquedas rápidas múltiples
- ✅ Manejo de delays de red
- ✅ Tests responsive
- ✅ Navegación por teclado
- ✅ Tests data-driven

## 🏗️ Arquitectura

### Page Object Model (POM)

El proyecto utiliza el patrón Page Object Model para mantener el código organizado y reutilizable:

- **BasePage**: Clase base con funcionalidad común
- **GoogleHomePage**: Página de inicio de Google
- **GoogleSearchResultsPage**: Página de resultados de búsqueda

### Comandos Personalizados

Comandos específicos para Google Search:
- `cy.visitGoogle()` - Navegar a Google
- `cy.searchGoogle(term)` - Realizar búsqueda
- `cy.verifySearchResults(term)` - Verificar resultados
- `cy.clickFirstSearchResult()` - Hacer clic en primer resultado

### Utilidades

- **dataHelpers**: Manejo de datos de prueba
- **testHelpers**: Funciones de apoyo para testing
- **reportHelpers**: Generación de reportes
- **environmentHelpers**: Configuración por ambiente

## 🔧 Configuración

### Ambientes

El proyecto soporta múltiples ambientes:

- **development**: Desarrollo local
- **staging**: Ambiente de pruebas
- **production**: Ambiente de producción  
- **ci**: Integración continua

### Variables de Ambiente

```javascript
// cypress.config.js
env: {
  testEnvironment: 'development',
  apiTimeout: 10000,
  enableRetries: true,
  enableScreenshots: true,
  enableVideos: true
}
```

### Configuración por Browser

El proyecto incluye configuraciones optimizadas para:
- Chrome (configuración por defecto)
- Firefox (preferencias específicas)
- Edge (argumentos optimizados)

## 🚀 CI/CD con Azure DevOps

### Pipeline Configurado

El archivo `azure-pipelines.yml` incluye:

1. **Triggers**: Push a main/develop, PRs
2. **Multi-browser testing**: Chrome, Firefox, Edge
3. **Artifacts**: Screenshots, videos, reportes
4. **Test results**: Publicación automática
5. **Caching**: node_modules para velocidad

### Stages del Pipeline

1. **Test Stage**: Ejecuta tests en paralelo por browser
2. **Report Stage**: Genera reportes consolidados

### Configuración en Azure DevOps

1. Crear nuevo pipeline
2. Seleccionar repositorio
3. Usar archivo `azure-pipelines.yml` existente
4. Configurar variables si es necesario
5. Ejecutar pipeline

## 📊 Reportes

### Tipos de Reportes

1. **Screenshots**: Automáticos en fallos
2. **Videos**: Grabación completa de ejecución
3. **JUnit XML**: Para integración con CI/CD
4. **HTML Reports**: Reportes visuales detallados

### Ubicación de Reportes

- Screenshots: `cypress/screenshots/`
- Videos: `cypress/videos/`
- Resultados XML: `cypress/results/`
- Reportes HTML: `cypress/reports/`

## 🐛 Debugging

### Tips para Debugging

1. **Usar modo interactivo**: `npm run cy:open`
2. **Screenshots automáticos**: Habilitados en fallos
3. **Videos**: Grabación completa disponible
4. **Logs detallados**: Usar `cy.log()` y `logTestStep()`
5. **DevTools**: Disponibles en modo interactivo

### Comandos de Debug

```javascript
// En tests
cy.debug()          // Pausa ejecución
cy.pause()          // Pausa con interfaz
cy.log('mensaje')   // Log personalizado
```

## 🤝 Contribución

### Estándares de Código

1. Usar ESLint para consistencia
2. Seguir convenciones de nomenclatura
3. Documentar funciones complejas
4. Escribir tests descriptivos
5. Usar Page Object Model

### Proceso de Contribución

1. Fork del repositorio
2. Crear branch feature
3. Implementar cambios
4. Ejecutar tests localmente
5. Crear Pull Request

## 🔍 Ejemplos de Uso

### Ejemplo básico de test
```javascript
import { GoogleHomePage } from '../support/pages'

describe('Google Search', () => {
  it('should perform a search', () => {
    const googleHomePage = new GoogleHomePage()

    googleHomePage
      .visit()
      .verifyPageLoaded()
      .search('Cypress testing')
      .verifySearchResults('Cypress testing')
  })
})
```

### Ejemplo con datos de prueba
```javascript
cy.fixture('testData').then((data) => {
  data.searchTerms.forEach((term) => {
    googleHomePage.search(term)
  })
})
```

## 📚 Recursos Adicionales

- [Documentación oficial de Cypress](https://docs.cypress.io/)
- [Best Practices de Cypress](https://docs.cypress.io/guides/references/best-practices)
- [Azure DevOps Pipelines](https://docs.microsoft.com/en-us/azure/devops/pipelines/)
- [Page Object Model Pattern](https://martinfowler.com/bliki/PageObject.html)

## 📄 Licencia

MIT License - ver archivo LICENSE para detalles.

## 👥 Autores

- Tu Nombre - Desarrollador Principal

## 🆘 Soporte

Para soporte y preguntas:
1. Revisar documentación
2. Buscar en issues existentes
3. Crear nuevo issue con detalles
4. Contactar al equipo de desarrollo
