/**
 * Base Page Object class with common functionality
 */
export class BasePage {
  constructor() {
    this.timeout = 10000
  }

  /**
   * Visit a URL
   * @param {string} url - URL to visit
   * @param {Object} options - Visit options
   */
  visit(url, options = {}) {
    cy.visit(url, options)
    this.waitForPageLoad()
    return this
  }

  /**
   * Wait for page to load completely
   */
  waitForPageLoad() {
    cy.waitForPageLoad()
    return this
  }

  /**
   * Get element by selector
   * @param {string} selector - CSS selector
   * @param {Object} options - Cypress get options
   * @returns {Cypress.Chainable} Cypress element
   */
  getElement(selector, options = {}) {
    return cy.get(selector, { timeout: this.timeout, ...options })
  }

  /**
   * Get element by data-testid
   * @param {string} testId - Test ID value
   * @param {Object} options - Cypress get options
   * @returns {Cypress.Chainable} Cypress element
   */
  getByTestId(testId, options = {}) {
    return this.getElement(`[data-testid="${testId}"]`, options)
  }

  /**
   * Get element by text content
   * @param {string} text - Text content to search for
   * @param {Object} options - Cypress contains options
   * @returns {Cypress.Chainable} Cypress element
   */
  getByText(text, options = {}) {
    return cy.contains(text, { timeout: this.timeout, ...options })
  }

  /**
   * Click element
   * @param {string} selector - CSS selector
   * @param {Object} options - Click options
   */
  clickElement(selector, options = {}) {
    this.getElement(selector).click(options)
    return this
  }

  /**
   * Type text into element
   * @param {string} selector - CSS selector
   * @param {string} text - Text to type
   * @param {Object} options - Type options
   */
  typeText(selector, text, options = {}) {
    this.getElement(selector).clear().type(text, options)
    return this
  }

  /**
   * Verify element is visible
   * @param {string} selector - CSS selector
   */
  verifyElementVisible(selector) {
    this.getElement(selector).should('be.visible')
    return this
  }

  /**
   * Verify element contains text
   * @param {string} selector - CSS selector
   * @param {string} text - Expected text
   */
  verifyElementContainsText(selector, text) {
    this.getElement(selector).should('contain.text', text)
    return this
  }

  /**
   * Verify page URL
   * @param {string} expectedUrl - Expected URL or URL pattern
   */
  verifyUrl(expectedUrl) {
    cy.url().should('include', expectedUrl)
    return this
  }

  /**
   * Verify page title
   * @param {string} expectedTitle - Expected title
   */
  verifyTitle(expectedTitle) {
    cy.title().should('include', expectedTitle)
    return this
  }

  /**
   * Take screenshot
   * @param {string} name - Screenshot name
   */
  takeScreenshot(name) {
    cy.takeScreenshotWithTimestamp(name)
    return this
  }

  /**
   * Wait for element to be stable
   * @param {string} selector - CSS selector
   * @param {number} timeout - Timeout in milliseconds
   */
  waitForStableElement(selector, timeout = 5000) {
    cy.waitForStableElement(selector, timeout)
    return this
  }

  /**
   * Handle cookie consent if present
   */
  handleCookieConsent() {
    cy.handleCookieConsent()
    return this
  }
}
