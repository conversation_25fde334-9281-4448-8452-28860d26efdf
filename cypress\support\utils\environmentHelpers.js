/**
 * Environment helper utilities for managing different test environments
 */

/**
 * Get current environment
 * @returns {string} Current environment name
 */
export const getCurrentEnvironment = () => {
  return Cypress.env('testEnvironment') || 'development'
}

/**
 * Check if running in CI environment
 * @returns {boolean} True if running in CI
 */
export const isCI = () => {
  return Cypress.env('CI') || process.env.CI || getCurrentEnvironment() === 'ci'
}

/**
 * Check if running in development environment
 * @returns {boolean} True if running in development
 */
export const isDevelopment = () => {
  return getCurrentEnvironment() === 'development'
}

/**
 * Check if running in production environment
 * @returns {boolean} True if running in production
 */
export const isProduction = () => {
  return getCurrentEnvironment() === 'production'
}

/**
 * Get environment-specific configuration
 * @param {string} environment - Environment name
 * @returns {Object} Environment configuration
 */
export const getEnvironmentConfig = (environment = getCurrentEnvironment()) => {
  const configs = {
    development: {
      baseUrl: 'https://www.google.com',
      timeout: 10000,
      retries: 0,
      video: false,
      screenshots: true,
      headless: false
    },
    staging: {
      baseUrl: 'https://www.google.com',
      timeout: 15000,
      retries: 1,
      video: true,
      screenshots: true,
      headless: true
    },
    production: {
      baseUrl: 'https://www.google.com',
      timeout: 20000,
      retries: 2,
      video: true,
      screenshots: true,
      headless: true
    },
    ci: {
      baseUrl: 'https://www.google.com',
      timeout: 30000,
      retries: 2,
      video: true,
      screenshots: true,
      headless: true
    }
  }
  
  return configs[environment] || configs.development
}

/**
 * Get browser-specific configuration
 * @returns {Object} Browser configuration
 */
export const getBrowserConfig = () => {
  const browser = Cypress.browser.name
  
  const configs = {
    chrome: {
      args: [
        '--disable-dev-shm-usage',
        '--no-sandbox',
        '--disable-gpu'
      ],
      preferences: {}
    },
    firefox: {
      args: [],
      preferences: {
        'dom.webnotifications.enabled': false,
        'dom.push.enabled': false
      }
    },
    edge: {
      args: [
        '--disable-dev-shm-usage',
        '--no-sandbox'
      ],
      preferences: {}
    }
  }
  
  return configs[browser] || configs.chrome
}

/**
 * Set environment-specific timeouts
 */
export const setEnvironmentTimeouts = () => {
  const config = getEnvironmentConfig()
  
  Cypress.config('defaultCommandTimeout', config.timeout)
  Cypress.config('requestTimeout', config.timeout)
  Cypress.config('responseTimeout', config.timeout)
}

/**
 * Get test data based on environment
 * @param {string} dataType - Type of test data
 * @returns {*} Environment-specific test data
 */
export const getEnvironmentTestData = (dataType) => {
  const environment = getCurrentEnvironment()
  
  const testData = {
    development: {
      searchTerms: [
        'Cypress testing development',
        'Local testing framework'
      ],
      users: {
        testUser: { name: 'Dev User', email: '<EMAIL>' }
      }
    },
    staging: {
      searchTerms: [
        'Cypress testing staging',
        'Staging environment testing'
      ],
      users: {
        testUser: { name: 'Stage User', email: '<EMAIL>' }
      }
    },
    production: {
      searchTerms: [
        'Cypress testing production',
        'Production testing framework'
      ],
      users: {
        testUser: { name: 'Prod User', email: '<EMAIL>' }
      }
    },
    ci: {
      searchTerms: [
        'Cypress CI testing',
        'Continuous integration testing'
      ],
      users: {
        testUser: { name: 'CI User', email: '<EMAIL>' }
      }
    }
  }
  
  return testData[environment]?.[dataType] || testData.development[dataType]
}

/**
 * Configure test based on environment
 */
export const configureTestEnvironment = () => {
  const environment = getCurrentEnvironment()
  const config = getEnvironmentConfig(environment)
  
  // Set timeouts
  setEnvironmentTimeouts()
  
  // Configure retries
  if (isCI()) {
    Cypress.config('retries', { runMode: 2, openMode: 0 })
  }
  
  // Configure video and screenshots
  Cypress.config('video', config.video)
  Cypress.config('screenshotOnRunFailure', config.screenshots)
  
  cy.log(`Test environment configured: ${environment}`)
}

/**
 * Get environment-specific URLs
 * @returns {Object} Environment URLs
 */
export const getEnvironmentUrls = () => {
  const environment = getCurrentEnvironment()
  
  const urls = {
    development: {
      google: 'https://www.google.com',
      googleSearch: 'https://www.google.com/search'
    },
    staging: {
      google: 'https://www.google.com',
      googleSearch: 'https://www.google.com/search'
    },
    production: {
      google: 'https://www.google.com',
      googleSearch: 'https://www.google.com/search'
    },
    ci: {
      google: 'https://www.google.com',
      googleSearch: 'https://www.google.com/search'
    }
  }
  
  return urls[environment] || urls.development
}

/**
 * Log environment information
 */
export const logEnvironmentInfo = () => {
  const info = {
    environment: getCurrentEnvironment(),
    isCI: isCI(),
    browser: Cypress.browser.name,
    viewport: {
      width: Cypress.config('viewportWidth'),
      height: Cypress.config('viewportHeight')
    },
    baseUrl: Cypress.config('baseUrl'),
    timeout: Cypress.config('defaultCommandTimeout')
  }
  
  cy.task('log', `Environment Info: ${JSON.stringify(info, null, 2)}`)
}
