# Guía de Configuración Detallada

Esta guía proporciona instrucciones paso a paso para configurar el proyecto de automatización Cypress.

## 📋 Prerrequisitos

### Software Requerido

1. **Node.js** (versión 16 o superior)
   - <PERSON><PERSON><PERSON> desde: https://nodejs.org/
   - Verificar instalación: `node --version`

2. **npm** (versión 8 o superior)
   - Incluido con Node.js
   - Verificar instalación: `npm --version`

3. **Git**
   - <PERSON><PERSON><PERSON> desde: https://git-scm.com/
   - Verificar instalación: `git --version`

### Navegadores Soportados

- **Chrome** (recomendado)
- **Firefox**
- **Edge**

## 🚀 Instalación Paso a Paso

### 1. Clonar el Repositorio

```bash
# Clonar el repositorio
git clone <repository-url>

# Navegar al directorio
cd CypressAutomation

# Verificar contenido
ls -la
```

### 2. Instalar Dependencias

```bash
# Instalar todas las dependencias
npm install

# Verificar instalación
npm list --depth=0
```

### 3. Verificar Cypress

```bash
# Verificar instalación de Cypress
npx cypress verify

# Información de Cypress
npx cypress info
```

### 4. Configuración Inicial

```bash
# Crear directorios necesarios
mkdir -p cypress/results
mkdir -p cypress/reports
mkdir -p cypress/screenshots
mkdir -p cypress/videos

# Verificar estructura
tree cypress/
```

## ⚙️ Configuración por Ambiente

### Desarrollo Local

1. **Variables de ambiente**
   ```bash
   export CYPRESS_testEnvironment=development
   export CYPRESS_video=false
   export CYPRESS_screenshotOnRunFailure=true
   ```

2. **Ejecutar primer test**
   ```bash
   npm run cy:open
   ```

### Ambiente de CI

1. **Configurar variables**
   ```bash
   export CYPRESS_testEnvironment=ci
   export CYPRESS_video=true
   export CYPRESS_screenshotOnRunFailure=true
   export CYPRESS_retries=2
   ```

2. **Ejecutar script de configuración**
   ```bash
   node scripts/ci-setup.js
   ```

## 🔧 Configuración de IDE

### Visual Studio Code

1. **Extensiones recomendadas**
   - Cypress Snippets
   - ES6 Mocha Snippets
   - JavaScript (ES6) code snippets

2. **Configuración de workspace**
   ```json
   {
     "recommendations": [
       "ms-vscode.vscode-typescript-next",
       "bradlc.vscode-tailwindcss",
       "esbenp.prettier-vscode"
     ]
   }
   ```

### IntelliJ IDEA / WebStorm

1. **Plugins recomendados**
   - Cypress Support
   - Node.js
   - JavaScript and TypeScript

2. **Configuración de run configurations**
   - Crear configuración para `npm run cy:open`
   - Crear configuración para `npm run cy:run`

## 🌐 Configuración de Navegadores

### Chrome

```javascript
// cypress.config.js
on('before:browser:launch', (browser, launchOptions) => {
  if (browser.name === 'chrome') {
    launchOptions.args.push('--disable-dev-shm-usage')
    launchOptions.args.push('--no-sandbox')
    launchOptions.args.push('--disable-gpu')
  }
  return launchOptions
})
```

### Firefox

```javascript
// cypress.config.js
on('before:browser:launch', (browser, launchOptions) => {
  if (browser.name === 'firefox') {
    launchOptions.preferences['dom.webnotifications.enabled'] = false
    launchOptions.preferences['dom.push.enabled'] = false
  }
  return launchOptions
})
```

## 🐳 Configuración con Docker

### Dockerfile

```dockerfile
FROM cypress/included:latest

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .

CMD ["npm", "run", "test:ci"]
```

### docker-compose.yml

```yaml
version: '3.8'
services:
  cypress:
    build: .
    volumes:
      - ./cypress/screenshots:/app/cypress/screenshots
      - ./cypress/videos:/app/cypress/videos
      - ./cypress/results:/app/cypress/results
    environment:
      - CYPRESS_testEnvironment=ci
```

## 🔍 Verificación de Configuración

### Script de Verificación

```bash
#!/bin/bash

echo "Verificando configuración..."

# Verificar Node.js
if command -v node &> /dev/null; then
    echo "✅ Node.js: $(node --version)"
else
    echo "❌ Node.js no encontrado"
    exit 1
fi

# Verificar npm
if command -v npm &> /dev/null; then
    echo "✅ npm: $(npm --version)"
else
    echo "❌ npm no encontrado"
    exit 1
fi

# Verificar Cypress
if npx cypress verify &> /dev/null; then
    echo "✅ Cypress verificado"
else
    echo "❌ Cypress no verificado"
    exit 1
fi

# Verificar estructura de directorios
if [ -d "cypress/e2e" ]; then
    echo "✅ Estructura de directorios correcta"
else
    echo "❌ Estructura de directorios incorrecta"
    exit 1
fi

echo "🎉 Configuración completada exitosamente!"
```

## 🚨 Solución de Problemas Comunes

### Error: Cypress binary not found

```bash
# Reinstalar Cypress
npm uninstall cypress
npm install cypress
npx cypress install
```

### Error: Permission denied

```bash
# En Linux/Mac
sudo chown -R $(whoami) ~/.cache/Cypress
sudo chown -R $(whoami) node_modules
```

### Error: Display not found (Linux)

```bash
# Instalar dependencias de sistema
sudo apt-get install xvfb
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
```

### Error: Chrome not found

```bash
# Ubuntu/Debian
sudo apt-get install google-chrome-stable

# CentOS/RHEL
sudo yum install google-chrome-stable
```

## 📝 Configuración Personalizada

### Variables de Ambiente Personalizadas

```javascript
// cypress.config.js
env: {
  // URLs personalizadas
  customBaseUrl: 'https://mi-app.com',
  apiUrl: 'https://api.mi-app.com',
  
  // Timeouts personalizados
  customTimeout: 15000,
  apiTimeout: 30000,
  
  // Configuraciones de usuario
  testUser: '<EMAIL>',
  testPassword: 'password123'
}
```

### Comandos Personalizados

```javascript
// cypress/support/commands.js
Cypress.Commands.add('loginCustom', (email, password) => {
  cy.visit('/login')
  cy.get('[data-testid="email"]').type(email)
  cy.get('[data-testid="password"]').type(password)
  cy.get('[data-testid="login-button"]').click()
})
```

## ✅ Lista de Verificación Final

- [ ] Node.js instalado (v16+)
- [ ] npm instalado (v8+)
- [ ] Git instalado
- [ ] Repositorio clonado
- [ ] Dependencias instaladas (`npm install`)
- [ ] Cypress verificado (`npx cypress verify`)
- [ ] Directorios creados
- [ ] Variables de ambiente configuradas
- [ ] IDE configurado
- [ ] Primer test ejecutado exitosamente
- [ ] Navegadores configurados
- [ ] Scripts de CI configurados (si aplica)

## 📞 Soporte

Si encuentras problemas durante la configuración:

1. Revisar logs de error completos
2. Verificar versiones de software
3. Consultar documentación oficial de Cypress
4. Buscar en issues del repositorio
5. Contactar al equipo de desarrollo
