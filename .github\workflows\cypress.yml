# GitHub Actions workflow for Cypress tests (alternative to Azure DevOps)
name: Cypress Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'cypress/**'
      - 'package.json'
      - 'cypress.config.js'
      - '.github/workflows/cypress.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'cypress/**'
      - 'package.json'
      - 'cypress.config.js'

jobs:
  cypress-run:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        browser: [chrome, firefox, edge]
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run Cypress tests
        uses: cypress-io/github-action@v6
        with:
          browser: ${{ matrix.browser }}
          command: npm run test:ci
        env:
          CYPRESS_testEnvironment: ci
          CYPRESS_video: true
          CYPRESS_screenshotOnRunFailure: true
      
      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots-${{ matrix.browser }}
          path: cypress/screenshots
      
      - name: Upload videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos-${{ matrix.browser }}
          path: cypress/videos
      
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-results-${{ matrix.browser }}
          path: cypress/results

  report:
    runs-on: ubuntu-latest
    needs: cypress-run
    if: always()
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: Generate consolidated report
        run: |
          echo "Generating consolidated test report..."
          # Add custom report generation logic here
      
      - name: Upload consolidated report
        uses: actions/upload-artifact@v4
        with:
          name: cypress-consolidated-report
          path: cypress/reports
