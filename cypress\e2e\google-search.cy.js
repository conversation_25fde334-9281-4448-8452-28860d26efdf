import { GoogleHomePage, GoogleSearchResultsPage } from '../support/pages'
import { getRandomSearchTerm, getSearchTerms } from '../support/utils/dataHelpers'
import { logTestStep } from '../support/utils/testHelpers'

describe('Google Search Tests', () => {
  let googleHomePage
  let testData

  before(() => {
    // Load test data
    cy.fixture('testData').then((data) => {
      testData = data
    })
  })

  beforeEach(() => {
    // Initialize page objects
    googleHomePage = new GoogleHomePage()
    
    // Visit Google homepage
    googleHomePage.visit()
  })

  describe('Homepage Functionality', () => {
    it('should load Google homepage successfully', () => {
      logTestStep('Verifying Google homepage is loaded')
      
      googleHomePage
        .verifyPageLoaded()
        .verifyLanguageLinks()
        .verifyFooter()
      
      logTestStep('Google homepage loaded successfully')
    })

    it('should display search box and buttons', () => {
      logTestStep('Verifying search elements are visible')
      
      googleHomePage.getSearchBox().should('be.visible')
      googleHomePage.getSearchButton().should('be.visible')
      googleHomePage.getLuckyButton().should('be.visible')
      
      logTestStep('All search elements are visible')
    })

    it('should show search suggestions when typing', () => {
      logTestStep('Testing search suggestions functionality')
      
      googleHomePage
        .typeSearchTerm('cypress testing')
        .verifySearchSuggestions()
      
      logTestStep('Search suggestions displayed successfully')
    })
  })

  describe('Search Functionality', () => {
    it('should perform basic search successfully', () => {
      const searchTerm = 'Cypress automation testing'
      logTestStep(`Performing search for: ${searchTerm}`)
      
      const searchResultsPage = googleHomePage.search(searchTerm)
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(searchTerm)
        .verifySearchStats()
      
      // Verify we have results
      searchResultsPage.getResultsCount().should('be.greaterThan', 0)
      
      logTestStep('Basic search completed successfully')
    })

    it('should search using search button', () => {
      const searchTerm = 'JavaScript testing framework'
      logTestStep(`Searching using search button for: ${searchTerm}`)
      
      googleHomePage.typeSearchTerm(searchTerm)
      const searchResultsPage = googleHomePage.clickSearchButton()
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(searchTerm)
      
      logTestStep('Search using button completed successfully')
    })

    it('should search using Enter key', () => {
      const searchTerm = 'End-to-end testing best practices'
      logTestStep(`Searching using Enter key for: ${searchTerm}`)
      
      googleHomePage.typeSearchTerm(searchTerm)
      const searchResultsPage = googleHomePage.pressEnterToSearch()
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(searchTerm)
      
      logTestStep('Search using Enter key completed successfully')
    })

    it('should perform multiple searches with different terms', () => {
      logTestStep('Testing multiple search terms')
      
      testData.searchTerms.forEach((searchTerm, index) => {
        logTestStep(`Search ${index + 1}: ${searchTerm}`)
        
        if (index === 0) {
          // First search from homepage
          const searchResultsPage = googleHomePage.search(searchTerm)
          searchResultsPage.verifySearchResults(searchTerm)
        } else {
          // Subsequent searches from results page
          const searchResultsPage = new GoogleSearchResultsPage()
          searchResultsPage.searchAgain(searchTerm)
          searchResultsPage.verifySearchResults(searchTerm)
        }
        
        cy.wait(1000) // Brief pause between searches
      })
      
      logTestStep('Multiple searches completed successfully')
    })

    it('should handle random search terms', () => {
      const randomSearchTerm = getRandomSearchTerm()
      logTestStep(`Performing random search for: ${randomSearchTerm}`)
      
      const searchResultsPage = googleHomePage.search(randomSearchTerm)
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchResults(randomSearchTerm)
      
      // Verify first result is clickable
      searchResultsPage.getFirstResult().should('be.visible')
      
      logTestStep('Random search completed successfully')
    })
  })

  describe('Search Results Interaction', () => {
    beforeEach(() => {
      // Perform a search before each test in this suite
      googleHomePage.search('Cypress testing framework')
    })

    it('should display search results with proper structure', () => {
      logTestStep('Verifying search results structure')
      
      const searchResultsPage = new GoogleSearchResultsPage()
      
      searchResultsPage
        .verifyPageLoaded()
        .verifySearchStats()
      
      // Verify results have titles and links
      searchResultsPage.getResultsCount().then((count) => {
        expect(count).to.be.greaterThan(0)
        logTestStep(`Found ${count} search results`)
        
        // Check first few results have proper structure
        for (let i = 0; i < Math.min(3, count); i++) {
          searchResultsPage.getResultByIndex(i)
            .find('h3')
            .should('be.visible')
          
          searchResultsPage.getResultByIndex(i)
            .find('a')
            .should('have.attr', 'href')
        }
      })
      
      logTestStep('Search results structure verified')
    })

    it('should be able to click on search results', () => {
      logTestStep('Testing search result click functionality')
      
      const searchResultsPage = new GoogleSearchResultsPage()
      
      // Get the first result title and URL before clicking
      searchResultsPage.getResultTitle(0).then((title) => {
        logTestStep(`Clicking on result: ${title}`)
        
        searchResultsPage.clickFirstResult()
        
        // Verify we navigated away from Google
        cy.url().should('not.include', 'google.com/search')
        
        logTestStep('Successfully clicked on search result')
      })
    })

    it('should handle pagination if available', () => {
      logTestStep('Testing pagination functionality')
      
      const searchResultsPage = new GoogleSearchResultsPage()
      
      // Check if next button exists
      cy.get('body').then(($body) => {
        if ($body.find('#pnnext').length > 0) {
          logTestStep('Next button found, testing pagination')
          
          searchResultsPage.goToNextPage()
          searchResultsPage.verifyPageLoaded()
          
          // Verify we're on page 2
          cy.url().should('include', 'start=')
          
          logTestStep('Pagination test completed')
        } else {
          logTestStep('No pagination available for this search')
        }
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle empty search gracefully', () => {
      logTestStep('Testing empty search handling')
      
      // Try to search with empty string
      googleHomePage.getSearchBox().clear()
      googleHomePage.getSearchButton().click()
      
      // Should remain on homepage or show appropriate message
      cy.url().then((url) => {
        if (url.includes('search')) {
          // If search was performed, verify appropriate handling
          const searchResultsPage = new GoogleSearchResultsPage()
          searchResultsPage.verifyPageLoaded()
        } else {
          // Should remain on homepage
          googleHomePage.verifyPageLoaded()
        }
      })
      
      logTestStep('Empty search handled appropriately')
    })

    it('should handle special characters in search', () => {
      const specialSearchTerm = 'test@#$%^&*()_+{}|:"<>?'
      logTestStep(`Testing special characters search: ${specialSearchTerm}`)
      
      const searchResultsPage = googleHomePage.search(specialSearchTerm)
      
      // Should handle gracefully without errors
      searchResultsPage.verifyPageLoaded()
      
      logTestStep('Special characters search handled successfully')
    })
  })

  afterEach(() => {
    // Take screenshot on failure
    if (Cypress.currentTest.state === 'failed') {
      cy.takeScreenshotWithTimestamp(`failed-${Cypress.currentTest.title}`)
    }
  })
})
