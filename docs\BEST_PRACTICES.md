# Mejores Prácticas para Cypress

Esta guía contiene las mejores prácticas implementadas en este proyecto y recomendaciones para mantener tests de alta calidad.

## 🏗️ Arquitectura y Organización

### Page Object Model (POM)

✅ **Hacer:**
```javascript
// ✅ Usar Page Object Model
class GoogleHomePage extends BasePage {
  search(term) {
    this.getSearchBox().type(term)
    this.getSearchBox().type('{enter}')
    return new GoogleSearchResultsPage()
  }
}
```

❌ **No hacer:**
```javascript
// ❌ Selectores directos en tests
cy.get('[name="q"]').type('search term')
cy.get('[name="btnK"]').click()
```

### Estructura de Archivos

✅ **Organización recomendada:**
```
cypress/
├── e2e/                    # Tests por funcionalidad
├── support/
│   ├── pages/             # Page Objects
│   ├── commands/          # Comandos personalizados
│   └── utils/             # Utilidades
└── fixtures/              # Datos de prueba
```

## 🎯 Escritura de Tests

### Nombres Descriptivos

✅ **Hacer:**
```javascript
describe('Google Search Functionality', () => {
  it('should display search results when user searches for valid term', () => {
    // test implementation
  })
  
  it('should handle empty search gracefully', () => {
    // test implementation
  })
})
```

❌ **No hacer:**
```javascript
describe('Tests', () => {
  it('test1', () => {
    // test implementation
  })
})
```

### Selectores Robustos

✅ **Prioridad de selectores:**
1. `[data-testid="element"]` - Más estable
2. `[data-cy="element"]` - Específico para Cypress
3. `#id` - Si es único y estable
4. `.class` - Si es específica
5. `element[attribute="value"]` - Atributos estables

❌ **Evitar:**
```javascript
// ❌ Selectores frágiles
cy.get('div > div > span:nth-child(3)')
cy.get('.css-1234567') // Clases generadas
```

### Aserciones Claras

✅ **Hacer:**
```javascript
// ✅ Aserciones específicas y claras
cy.get('[data-testid="search-results"]')
  .should('be.visible')
  .and('contain.text', 'Cypress')
  .and('have.length.greaterThan', 0)
```

❌ **No hacer:**
```javascript
// ❌ Aserciones vagas
cy.get('.results').should('exist')
```

## ⚡ Performance y Estabilidad

### Esperas Inteligentes

✅ **Hacer:**
```javascript
// ✅ Esperar condiciones específicas
cy.get('[data-testid="loading"]').should('not.exist')
cy.get('[data-testid="results"]').should('be.visible')

// ✅ Usar intercepts para APIs
cy.intercept('GET', '/api/search').as('searchAPI')
cy.wait('@searchAPI')
```

❌ **No hacer:**
```javascript
// ❌ Esperas fijas
cy.wait(5000)
```

### Manejo de Elementos Dinámicos

✅ **Hacer:**
```javascript
// ✅ Verificar estabilidad
cy.waitForStableElement('[data-testid="dynamic-element"]')

// ✅ Manejar elementos que aparecen/desaparecen
cy.get('body').then(($body) => {
  if ($body.find('[data-testid="modal"]').length > 0) {
    cy.get('[data-testid="close-modal"]').click()
  }
})
```

### Limpieza de Estado

✅ **Hacer:**
```javascript
beforeEach(() => {
  cy.clearCookies()
  cy.clearLocalStorage()
  cy.visit('/')
})

afterEach(() => {
  // Limpiar estado específico del test
})
```

## 🔧 Comandos Personalizados

### Comandos Reutilizables

✅ **Hacer:**
```javascript
// ✅ Comandos específicos del dominio
Cypress.Commands.add('searchGoogle', (term) => {
  cy.get('[name="q"]').type(term)
  cy.get('[name="btnK"]').click()
  cy.get('#search').should('be.visible')
})

// ✅ Comandos con parámetros opcionales
Cypress.Commands.add('login', (user = 'defaultUser') => {
  cy.fixture('users').then((users) => {
    const userData = users[user]
    cy.get('[data-testid="email"]').type(userData.email)
    cy.get('[data-testid="password"]').type(userData.password)
    cy.get('[data-testid="login-btn"]').click()
  })
})
```

### Comandos Chainables

✅ **Hacer:**
```javascript
// ✅ Comandos que retornan elementos
Cypress.Commands.add('getByTestId', (testId) => {
  return cy.get(`[data-testid="${testId}"]`)
})

// Uso
cy.getByTestId('search-box').type('test')
```

## 📊 Datos de Prueba

### Fixtures y Data Management

✅ **Hacer:**
```javascript
// ✅ Usar fixtures para datos
cy.fixture('testData').then((data) => {
  data.searchTerms.forEach((term) => {
    cy.searchGoogle(term)
  })
})

// ✅ Generar datos dinámicos
const testData = {
  email: `test-${Date.now()}@example.com`,
  name: faker.name.findName()
}
```

❌ **No hacer:**
```javascript
// ❌ Datos hardcodeados en tests
cy.type('<EMAIL>')
cy.type('password123')
```

### Datos por Ambiente

✅ **Hacer:**
```javascript
// ✅ Configuración por ambiente
const getTestData = () => {
  const env = Cypress.env('testEnvironment')
  return {
    development: { baseUrl: 'http://localhost:3000' },
    staging: { baseUrl: 'https://staging.app.com' },
    production: { baseUrl: 'https://app.com' }
  }[env]
}
```

## 🚨 Manejo de Errores

### Retry Logic

✅ **Hacer:**
```javascript
// ✅ Configurar retries en cypress.config.js
retries: {
  runMode: 2,
  openMode: 0
}

// ✅ Retry personalizado para operaciones específicas
cy.retryAction(() => {
  cy.get('[data-testid="flaky-element"]').click()
}, 3)
```

### Error Handling

✅ **Hacer:**
```javascript
// ✅ Manejar excepciones específicas
Cypress.on('uncaught:exception', (err, runnable) => {
  if (err.message.includes('Script error')) {
    return false // Ignorar errores de script
  }
  return true
})

// ✅ Verificar estados de error
cy.get('[data-testid="error-message"]')
  .should('not.exist')
  .or('not.be.visible')
```

## 📱 Cross-browser Testing

### Configuración Multi-browser

✅ **Hacer:**
```javascript
// ✅ Configurar browsers en package.json
{
  "scripts": {
    "test:chrome": "cypress run --browser chrome",
    "test:firefox": "cypress run --browser firefox",
    "test:edge": "cypress run --browser edge"
  }
}
```

### Browser-specific Code

✅ **Hacer:**
```javascript
// ✅ Código específico por browser cuando sea necesario
if (Cypress.browser.name === 'firefox') {
  // Configuración específica para Firefox
  cy.get('[data-testid="element"]', { timeout: 15000 })
} else {
  cy.get('[data-testid="element"]')
}
```

## 🔍 Debugging

### Logging y Screenshots

✅ **Hacer:**
```javascript
// ✅ Logs informativos
cy.log('Starting search test')
cy.task('log', 'Custom log message')

// ✅ Screenshots en puntos clave
cy.takeScreenshotWithTimestamp('before-search')

// ✅ Debug condicional
if (Cypress.env('debug')) {
  cy.debug()
}
```

### Test Isolation

✅ **Hacer:**
```javascript
// ✅ Tests independientes
describe('Search Tests', () => {
  beforeEach(() => {
    // Setup limpio para cada test
    cy.visit('/')
  })
  
  it('should search successfully', () => {
    // Test completamente independiente
  })
})
```

## 📈 CI/CD Best Practices

### Pipeline Configuration

✅ **Hacer:**
```yaml
# ✅ Pipeline paralelo por browser
strategy:
  matrix:
    browser: [chrome, firefox, edge]

# ✅ Artifacts para debugging
- task: PublishBuildArtifacts@1
  condition: always()
  inputs:
    pathToPublish: 'cypress/screenshots'
```

### Environment Variables

✅ **Hacer:**
```javascript
// ✅ Variables de ambiente para CI
env: {
  CI: true,
  testEnvironment: 'ci',
  video: true,
  screenshotOnRunFailure: true
}
```

## 🎨 Code Style

### Formatting y Linting

✅ **Hacer:**
```javascript
// ✅ Consistent formatting
describe('Feature Name', () => {
  beforeEach(() => {
    // Setup
  })
  
  it('should do something specific', () => {
    // Given
    cy.visit('/page')
    
    // When
    cy.get('[data-testid="button"]').click()
    
    // Then
    cy.get('[data-testid="result"]').should('be.visible')
  })
})
```

### Comentarios Útiles

✅ **Hacer:**
```javascript
// ✅ Comentarios que explican el "por qué"
// Wait for animation to complete before clicking
cy.wait(500)

// Handle cookie consent banner that appears randomly
cy.handleCookieConsent()
```

## 📋 Checklist de Calidad

Antes de hacer commit, verificar:

- [ ] Tests tienen nombres descriptivos
- [ ] Selectores son robustos y estables
- [ ] No hay esperas fijas (`cy.wait(number)`)
- [ ] Tests son independientes entre sí
- [ ] Datos de prueba están en fixtures
- [ ] Comandos personalizados son reutilizables
- [ ] Manejo de errores implementado
- [ ] Screenshots/videos configurados
- [ ] Tests pasan en múltiples browsers
- [ ] Pipeline de CI configurado
- [ ] Documentación actualizada

## 🚀 Performance Tips

1. **Usar `cy.intercept()`** para controlar requests de red
2. **Minimizar DOM queries** reutilizando elementos
3. **Configurar timeouts** apropiados por ambiente
4. **Usar `cy.task()`** para operaciones pesadas
5. **Implementar retry logic** para elementos flaky
6. **Limpiar estado** entre tests
7. **Usar fixtures** en lugar de APIs reales cuando sea posible
8. **Configurar caching** en CI para node_modules
