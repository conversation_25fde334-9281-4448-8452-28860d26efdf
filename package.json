{"name": "cypress-automation-project", "version": "1.0.0", "description": "Proyecto de automatización con Cypress siguiendo mejores prácticas", "main": "index.js", "scripts": {"cy:open": "cypress open", "cy:run": "cypress run", "cy:run:chrome": "cypress run --browser chrome", "cy:run:firefox": "cypress run --browser firefox", "cy:run:edge": "cypress run --browser edge", "cy:run:headed": "cypress run --headed", "cy:run:headless": "cypress run --headless", "test": "cypress run", "test:chrome": "cypress run --browser chrome", "test:firefox": "cypress run --browser firefox", "test:edge": "cypress run --browser edge", "test:headed": "cypress run --headed", "test:ci": "cypress run --browser chrome --headless --reporter junit --reporter-options mochaFile=cypress/results/test-results.xml", "clean:reports": "rimraf cypress/results cypress/screenshots cypress/videos", "pretest": "npm run clean:reports"}, "keywords": ["cypress", "automation", "testing", "e2e", "qa", "azure-devops"], "author": "Tu Nombre", "license": "MIT", "devDependencies": {"cypress": "^13.6.0", "@cypress/grep": "^4.0.1", "cypress-mochawesome-reporter": "^3.8.1", "cypress-multi-reporters": "^1.6.4", "mochawesome": "^7.1.3", "mochawesome-merge": "^4.3.0", "mochawesome-report-generator": "^6.2.0", "rimraf": "^5.0.5"}, "dependencies": {"faker": "^6.6.6"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}