# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Cypress
cypress/videos/
cypress/screenshots/
cypress/results/
cypress/reports/
cypress/downloads/

# Test artifacts
*.log
test-results.xml
junit.xml
mochawesome-report/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build outputs
dist/
build/
out/

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Coverage reports
coverage/
.nyc_output/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# Azure Functions artifacts
bin
obj
appsettings.json
local.settings.json

# Cypress binary cache
~/.cache/Cypress/

# MacOS
.AppleDouble
.LSOverride

# Windows
desktop.ini

# Linux
*~
