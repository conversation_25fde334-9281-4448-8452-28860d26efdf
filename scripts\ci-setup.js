#!/usr/bin/env node

/**
 * CI Setup Script
 * Prepares the environment for running Cypress tests in CI/CD
 */

const fs = require('fs')
const path = require('path')

/**
 * Create necessary directories for CI
 */
function createDirectories() {
  const directories = [
    'cypress/results',
    'cypress/reports',
    'cypress/screenshots',
    'cypress/videos',
    'cypress/artifacts'
  ]
  
  directories.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir)
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true })
      console.log(`Created directory: ${dir}`)
    }
  })
}

/**
 * Set up environment variables for CI
 */
function setupEnvironmentVariables() {
  const ciEnvVars = {
    CYPRESS_testEnvironment: 'ci',
    CYPRESS_video: 'true',
    CYPRESS_screenshotOnRunFailure: 'true',
    CYPRESS_retries: '2',
    CYPRESS_defaultCommandTimeout: '30000',
    CYPRESS_requestTimeout: '30000',
    CYPRESS_responseTimeout: '30000',
    CYPRESS_pageLoadTimeout: '60000'
  }
  
  Object.entries(ciEnvVars).forEach(([key, value]) => {
    if (!process.env[key]) {
      process.env[key] = value
      console.log(`Set environment variable: ${key}=${value}`)
    }
  })
}

/**
 * Validate Cypress installation
 */
function validateCypressInstallation() {
  try {
    const cypress = require('cypress')
    console.log('Cypress installation validated successfully')
    return true
  } catch (error) {
    console.error('Cypress installation validation failed:', error.message)
    return false
  }
}

/**
 * Create CI-specific configuration
 */
function createCIConfiguration() {
  const ciConfig = {
    reporter: 'junit',
    reporterOptions: {
      mochaFile: 'cypress/results/test-results-[hash].xml',
      toConsole: true
    },
    video: true,
    screenshotOnRunFailure: true,
    retries: {
      runMode: 2,
      openMode: 0
    },
    env: {
      testEnvironment: 'ci'
    }
  }
  
  const configPath = path.join(process.cwd(), 'cypress.ci.config.js')
  const configContent = `
const { defineConfig } = require('cypress')
const baseConfig = require('./cypress.config.js')

module.exports = defineConfig({
  ...baseConfig,
  e2e: {
    ...baseConfig.e2e,
    ...${JSON.stringify(ciConfig, null, 2)}
  }
})
`
  
  fs.writeFileSync(configPath, configContent)
  console.log('Created CI-specific configuration: cypress.ci.config.js')
}

/**
 * Install system dependencies for CI
 */
function installSystemDependencies() {
  const { execSync } = require('child_process')
  
  try {
    // Install system dependencies for headless browsers
    if (process.platform === 'linux') {
      console.log('Installing system dependencies for Linux...')
      execSync('sudo apt-get update', { stdio: 'inherit' })
      execSync('sudo apt-get install -y libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libgconf-2-4 libnss3 libxss1 libasound2 libxtst6 xauth xvfb', { stdio: 'inherit' })
    }
    
    console.log('System dependencies installed successfully')
  } catch (error) {
    console.warn('Warning: Could not install system dependencies:', error.message)
  }
}

/**
 * Generate test data for CI
 */
function generateTestData() {
  const testData = {
    searchTerms: [
      'Cypress CI testing',
      'Automated testing framework',
      'Continuous integration testing',
      'DevOps testing pipeline',
      'Quality assurance automation'
    ],
    users: {
      ciUser: {
        name: 'CI Test User',
        email: '<EMAIL>'
      }
    },
    urls: {
      google: 'https://www.google.com',
      googleSearch: 'https://www.google.com/search'
    },
    timeouts: {
      short: 5000,
      medium: 15000,
      long: 30000
    }
  }
  
  const testDataPath = path.join(process.cwd(), 'cypress/fixtures/ciTestData.json')
  fs.writeFileSync(testDataPath, JSON.stringify(testData, null, 2))
  console.log('Generated CI test data: cypress/fixtures/ciTestData.json')
}

/**
 * Main setup function
 */
function main() {
  console.log('Starting CI setup...')
  
  try {
    createDirectories()
    setupEnvironmentVariables()
    
    if (!validateCypressInstallation()) {
      process.exit(1)
    }
    
    createCIConfiguration()
    installSystemDependencies()
    generateTestData()
    
    console.log('CI setup completed successfully!')
  } catch (error) {
    console.error('CI setup failed:', error.message)
    process.exit(1)
  }
}

// Run setup if this script is executed directly
if (require.main === module) {
  main()
}

module.exports = {
  createDirectories,
  setupEnvironmentVariables,
  validateCypressInstallation,
  createCIConfiguration,
  installSystemDependencies,
  generateTestData
}
