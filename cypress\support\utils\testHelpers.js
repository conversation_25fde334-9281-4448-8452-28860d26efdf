/**
 * Test helper utilities for common testing operations
 */

/**
 * Wait for network requests to complete
 * @param {number} timeout - Timeout in milliseconds
 */
export const waitForNetworkIdle = (timeout = 2000) => {
  cy.window().then((win) => {
    let requestCount = 0
    
    // Intercept XMLHttpRequest
    const originalXHR = win.XMLHttpRequest
    win.XMLHttpRequest = function() {
      const xhr = new originalXHR()
      requestCount++
      
      xhr.addEventListener('loadend', () => {
        requestCount--
      })
      
      return xhr
    }
    
    // Intercept fetch
    const originalFetch = win.fetch
    win.fetch = function(...args) {
      requestCount++
      return originalFetch.apply(this, args).finally(() => {
        requestCount--
      })
    }
    
    // Wait for requests to complete
    cy.waitUntil(() => requestCount === 0, {
      timeout,
      interval: 100,
      errorMsg: `Network requests did not complete within ${timeout}ms`
    })
  })
}

/**
 * Retry an operation until it succeeds or max attempts reached
 * @param {Function} operation - Operation to retry
 * @param {number} maxAttempts - Maximum number of attempts
 * @param {number} delay - Delay between attempts in milliseconds
 */
export const retryOperation = (operation, maxAttempts = 3, delay = 1000) => {
  let attempts = 0
  
  const attempt = () => {
    attempts++
    
    try {
      return operation()
    } catch (error) {
      if (attempts < maxAttempts) {
        cy.wait(delay)
        return attempt()
      } else {
        throw new Error(`Operation failed after ${maxAttempts} attempts: ${error.message}`)
      }
    }
  }
  
  return attempt()
}

/**
 * Generate unique test identifier
 * @param {string} prefix - Prefix for the identifier
 * @returns {string} Unique identifier
 */
export const generateTestId = (prefix = 'test') => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}-${timestamp}-${random}`
}

/**
 * Log test step for better debugging
 * @param {string} step - Step description
 * @param {*} data - Optional data to log
 */
export const logTestStep = (step, data = null) => {
  const timestamp = new Date().toISOString()
  cy.log(`[${timestamp}] ${step}`)
  
  if (data) {
    cy.log('Data:', data)
  }
}

/**
 * Take screenshot with custom name and timestamp
 * @param {string} name - Screenshot name
 * @param {Object} options - Screenshot options
 */
export const takeTimestampedScreenshot = (name, options = {}) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const screenshotName = `${name}-${timestamp}`
  
  cy.screenshot(screenshotName, options)
}

/**
 * Verify element is in viewport
 * @param {string} selector - Element selector
 */
export const verifyElementInViewport = (selector) => {
  cy.get(selector).then(($el) => {
    const rect = $el[0].getBoundingClientRect()
    const windowHeight = Cypress.config('viewportHeight')
    const windowWidth = Cypress.config('viewportWidth')
    
    expect(rect.top).to.be.at.least(0)
    expect(rect.left).to.be.at.least(0)
    expect(rect.bottom).to.be.at.most(windowHeight)
    expect(rect.right).to.be.at.most(windowWidth)
  })
}
