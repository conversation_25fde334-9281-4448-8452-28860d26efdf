// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import custom commands
import './commands/googleCommands'

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // returning false here prevents <PERSON><PERSON> from
  // failing the test on uncaught exceptions
  if (err.message.includes('Script error')) {
    return false
  }
  return true
})

// Before each test
beforeEach(() => {
  // Clear cookies and local storage
  cy.clearCookies()
  cy.clearLocalStorage()
  
  // Set viewport size
  cy.viewport(1920, 1080)
})

// After each test
afterEach(() => {
  // Take screenshot on failure
  if (Cypress.currentTest.state === 'failed') {
    cy.screenshot(`failed-${Cypress.currentTest.title}`)
  }
})
