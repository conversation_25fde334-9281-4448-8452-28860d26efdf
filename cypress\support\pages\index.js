/**
 * Page Objects index file
 * Exports all page objects for easy importing
 */

export { BasePage } from './BasePage'
export { GoogleHomePage } from './GoogleHomePage'
export { GoogleSearchResultsPage } from './GoogleSearchResultsPage'

// Factory function to create page objects
export const PageFactory = {
  /**
   * Create Google Home Page instance
   * @returns {GoogleHomePage} Google Home Page instance
   */
  createGoogleHomePage() {
    const { GoogleHomePage } = require('./GoogleHomePage')
    return new GoogleHomePage()
  },

  /**
   * Create Google Search Results Page instance
   * @returns {GoogleSearchResultsPage} Google Search Results Page instance
   */
  createGoogleSearchResultsPage() {
    const { GoogleSearchResultsPage } = require('./GoogleSearchResultsPage')
    return new GoogleSearchResultsPage()
  }
}
